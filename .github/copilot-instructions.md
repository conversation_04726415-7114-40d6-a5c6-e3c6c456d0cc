# Copilot Instructions for Solana RPC Proxy

## Project Overview

High-performance JSON-RPC proxy server specifically designed for Solana blockchain. The proxy intelligently routes requests to multiple endpoints using various strategies to ensure optimal performance and reliability. This project is in early development stage, so architecture may evolve.

## Technical Stack

- **Runtime**: Latest Node.js LTS with ESM modules
- **Language**: Latest TypeScript with decorators
- **Package Manager**: pnpm
- **WebSocket Server**: uWebSockets.js for maximum performance
- **Database**: TypeORM with SQLite/PostgreSQL for logs and metrics
- **Core Libraries**: @kdt310722 packages (config, logger, rpc, utils)
- **Code Quality**: ESLint with custom configuration

## Important: Read Package Documentation

Before using any @kdt310722 package functionality, **ALWAYS** read the corresponding cheatsheet in the `docs` folder:

- `kdt310722-config-cheatsheet.md` - Configuration management patterns
- `kdt310722-logger-cheatsheet.md` - Logging best practices
- `kdt310722-utils-cheatsheet.md` - Utility functions reference
- **Use these packages correctly** by following their documented patterns and APIs

## Important: Check Existing Utilities First

**BEFORE writing any utility function**, ALWAYS check if it already exists in `@kdt310722/utils`:

- Array utilities: sorting, filtering, grouping, unique operations
- String utilities: formatting, validation, transformation
- Object utilities: deep operations, merging, picking/omitting
- Date/time utilities: formatting, parsing, calculations
- Type guards and validation helpers
- **Never reinvent the wheel** - use existing utilities to maintain consistency

## Project Structure

```
app/
├── common/        # Application logics
├── config/        # Configuration schemas with Zod
├── core/          # Core logics
├── entities/      # TypeORM entities
├── errors/        # Custom error classes
├── modules/       # Feature modules
├── utils/         # Utilities and helpers
└── workers/       # Background workers
```

## Code Style Guidelines (Strict Rules)

### General Formatting

- Use **4 spaces** for indentation (no tabs)
- **NO semicolons** at the end of statements
- Use **single quotes** for strings
- Template literals are allowed
- Unix line endings (LF)
- No trailing spaces
- Files must end with a newline

### Code Structure

- Use **1tbs brace style** (one true brace style)
- Always use curly braces for all control structures
- Single line blocks are allowed

### Spacing Rules

- Always have spaces inside object curly braces: `{ key: value }`
- No spaces inside array brackets: `[item1, item2]`
- No space before function parentheses for named functions: `function name()`
- Space for anonymous and async functions: `function () {}`, `async () => {}`
- Spaces before and after keywords: `if (condition) {}`

### Commas

- Always use trailing commas in multiline structures (except imports/exports)
- Space after comma, no space before: `item1, item2`

### Variables

- Use `const` by default
- Use `let` when reassignment is needed
- Never use `var`
- Only one variable per declaration (except uninitialized)
- Group variable declarations by type with blank lines between groups

### Functions

- Prefer arrow functions for callbacks
- Always use parentheses around arrow function parameters: `(param) => {}`
- **Function/method signatures must be declared on a single line**, regardless of parameter count (e.g., `public get(a: number, b: number, c: number) {`)
- **Omit explicit return types** unless absolutely necessary (e.g., when input parameter is Error type or for complex generic scenarios)
- **Maximum 30 lines per function**
- **Maximum 3 levels of nesting depth**
- **Prefix unused parameters with underscore**: `_error`, `_unused`
- For async functions returning single awaited expression, return directly

### Objects and Classes

- Use property shorthand when possible: `{ name }` instead of `{ name: name }`
- Blank line between class members (except single line)
- **Mandatory access modifiers** (public, private, protected) for ALL class methods including constructors
- **Prefer `protected` over `private`** for properties and methods to enable easier inheritance and extension by subclasses
- **Add `readonly` modifier** to properties that should not be modified after initialization

### Class Organization

1. **Public properties**
2. **Protected properties**
3. **Private properties** (prefer `#privateField` syntax)
4. **Constructor**
5. **Static methods**
6. **Instance methods** (public → protected → private)

### Class Formatting Rules

- Group class properties by access modifier with spacing
- Prefer inline access modifiers for simple constructors: `public constructor(private readonly config: Config) {}`
- Keep constructor declarations on single line when reasonable

### Module Organization

- Use `export * from './file'` pattern for re-exports
- Use `export type * from './types'` for type-only exports
- Export modules as namespaces when appropriate

### TypeScript Specific

- Use inline type imports: `import { type User } from './types'`
- Use array syntax for simple types: `string[]`
- Use generic syntax for complex types: `Array<string | number>`
- **NEVER use single line interfaces** - always multiline
- **Omit return types for simple types** (string, boolean, etc.)
- **Only add return types for complex cases**
- Prefer `unknown` over `any`
- **Function signatures should be on single lines when possible** for better readability
- **Consistent parameter destructuring patterns** with default values in function signatures

### Control Flow

- No `else` after `return`
- Use strict equality (`===` and `!==`)
- Keep ternary operators on single line

### Error Handling

- Only `console.warn` and `console.error` allowed (no `console.log`)
- Always provide error messages
- **Catch specific errors, not all errors**
- **Fail fast**: detect and report errors early
- **Include contextual information** in error messages

### Imports and Exports

- **Use `node:` prefix for Node.js built-in modules**
- Sort imports by type and alphabetically
- One blank line after import statements

### Import Order (Strict)

1. **Node.js built-in modules** (with `node:` prefix)
2. **External packages** (alphabetical)
3. **Side-effect imports** (`import 'module'`)
4. **Internal modules** (by proximity: `../`, `./`)

### Import Rules

- **Use `node:` prefix for Node.js built-in modules**
- Sort imports by type and alphabetically
- One blank line after import statements
- Keep each import on one line (no line breaks in import statements)
- Keep export statements on one line without line breaks
- No import type side effects

### Comments

- **NO comments in code** - code must be self-documenting
- If absolutely necessary, use meaningful variable/function names instead

### Code Quality

- Keep functions focused on single responsibility
- **Maximum 30 lines per function**
- **Maximum 3 levels of nesting**
- Avoid unnecessary async/await in direct returns
- Extract reusable logic into utility functions
- **Eliminate all unused code**: imports, variables, methods, parameters, and type definitions
- **NO test files** - focus on production code only

### Blank Lines

- Required around: classes, interfaces, functions, try/catch blocks
- No blank lines between `case` statements in `switch`

### File Naming

- Use **kebab-case** for all filenames
- Examples: `user-service.ts`, `database-config.ts`

## Core Features & Patterns

The proxy implements various routing strategies for different RPC methods:

- Some methods may broadcast to multiple endpoints
- Others may use smart routing based on performance metrics
- Strategies are configurable and may evolve as the project develops

Key architectural decisions are still being finalized, including:

- Endpoint selection algorithms
- Monitoring and metrics collection approaches
- Worker process implementations
- Database schema design

## Implementation Guidelines

### Scope Limitation Rule

- **Only implement exactly what is requested in the task**
- Do not create additional code, features, or functionality beyond the explicit requirements
- If you think there might be related improvements or additions, ask the user first before implementing
- Focus on the specific requirements and avoid scope creep
- Stick to the minimal viable implementation that satisfies the request
- **Do NOT create summary files** like `README.md` and examples after completing tasks unless explicitly requested
- **Strictly limit implementation** to only what is specifically requested - avoid adding unrelated features, utilities, or "nice-to-have" additions

### Default Exclusions Rule

By default, **skip the following unless explicitly requested**:

- **Input validation and parameter checking** - Only add when specifically asked
- **Documentation** - Comments, doc comments, README updates, or inline documentation
- **Example implementations** - Only create when the user asks for examples
- **Comprehensive error handling** - Only add when specifically requested

**Important**: Only create these items when the user specifically asks for them in their request. This keeps implementations focused and prevents unnecessary work.

### Code Quality Checks Rule

- **Never run automated checks unless explicitly requested** - Do not automatically run eslint, tsc, or other validation tools
- Only execute `pnpm lint`, `pnpm typecheck`, or similar commands when the user specifically asks for them
- Focus on implementing the requested changes without automatic validation unless verification is requested
- Let the user decide when to run quality checks and validation

### Configuration

- Use Zod schemas for all configuration validation
- Support environment-based configuration
- Keep configurations flexible for future changes

### Error Handling

- Use custom error classes for different error types
- Implement proper error propagation
- Log errors appropriately based on severity
- Create custom error types with additional context patterns
- Implement `withValue` pattern for additional error context
- Use proper error hierarchy and chain errors to preserve context
- Map domain-specific errors appropriately (especially for Solana errors)

### Performance

- Optimize for low latency
- Implement efficient request routing
- Monitor performance metrics
- Avoid unnecessary allocations
- Use iterators efficiently
- Profile before optimizing
- Consider zero-copy operations when possible
- Implement request/response streaming for better performance
- Handle backpressure and flow control in streaming scenarios

### Async/Concurrency Best Practices

- Handle async cancellation properly
- Use channels for communication between tasks
- Avoid blocking operations in async context
- Implement proper connection pooling
- Handle stream disconnections and reconnections gracefully

### WebSocket Specific Guidelines

- Use stream wrappers for consistent error handling
- Implement proper cleanup for streams and connections
- Use appropriate timeouts for RPC calls
- Implement heartbeat mechanisms for long-lived connections
- Handle WebSocket lifecycle events properly

### Database

- Use for logging and metrics only
- No application state or caching
- Design schema for future extensibility
- Implement utility functions like `upsert` and `chunkUpsert`
- Use custom column transformers for special data types
- Create reusable column decorators for common patterns
- Use QueryBuilder for complex queries efficiently

### Logging Best Practices

- Create root logger for entire application
- Use child loggers with hierarchical naming structure
- Log with appropriate levels (error, warn, info, debug, trace)
- Include structured contextual information in logs
- Separate loggers for different concerns (request, performance, errors)

### Memory Management Guidelines

- Understand ownership patterns
- Avoid memory leaks with proper cleanup
- Use weak references when appropriate
- Manage closures carefully to avoid memory issues

## Solana-Specific Considerations

- Handle Solana RPC method requirements
- Consider network-specific constraints
- Implement appropriate retry strategies

## When Writing Code

1. **Read relevant cheatsheets first** for @kdt310722 packages
2. **Check @kdt310722/utils** before creating any utility function
3. Follow all code style rules strictly
4. Keep implementations flexible for future changes
5. Use TypeScript types extensively
6. Handle errors at appropriate levels
7. **NO comments** - code must be self-explanatory
8. **NO test files** - production code only
9. Consider performance implications
10. Log important events for monitoring
