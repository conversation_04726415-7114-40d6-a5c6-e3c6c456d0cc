import { transform } from '@kdt310722/utils/function'
import { ParseUrlError } from '../errors/parse-url-error'

export interface ParsedUrl {
    origin: string
    path: string
}

export function parseUrl(url: string): ParsedUrl {
    try {
        return transform(new URL(url), (parsed) => ({ origin: parsed.origin, path: parsed.pathname + parsed.search + parsed.hash }))
    } catch {
        throw new ParseUrlError('Failed to parse URL').withUrl(url)
    }
}
