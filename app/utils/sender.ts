import { Pool } from 'undici'
import { parseUrl } from './urls'

export class Sender {
    public readonly origin: string
    public readonly path: string

    protected readonly pool: Pool

    #id = 0

    public constructor(url: string) {
        const { origin, path } = parseUrl(url)

        this.origin = origin
        this.path = path
        this.pool = this.createPool(origin)
    }

    public async send(): Promise<any> {
        console.log('id', ++this.#id)

        const response = await this.pool.request({
            method: 'GET',
            path: this.path,
        })

        return response
    }

    protected createPool(origin: string) {
        const pool = new Pool(origin, {})

        pool.on('connect', () => console.log(`Connected`))
        pool.on('disconnect', (_, __, error) => console.log(`Disconnected`, error))
        pool.on('connectionError', (_, __, error) => console.log(`Connection Error`, error))
        pool.on('drain', () => console.log(`Pool drained`))

        return pool
    }
}

const sender = new Sender('https://httpbin.org/get')

await Promise.all([sender.send(), sender.send(), sender.send()])
await sender.send()
await sender.send()
await sender.send()

setInterval(() => sender['pool'].sta, 5000)
