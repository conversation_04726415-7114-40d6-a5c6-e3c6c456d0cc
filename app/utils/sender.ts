import { Pool } from 'undici'
import { parseUrl } from './urls'

export class Sender {
    public readonly origin: string
    public readonly path: string

    protected readonly pool: Pool

    #id = 0

    public constructor(url: string) {
        const { origin, path } = parseUrl(url)

        this.origin = origin
        this.path = path
        this.pool = this.createPool(origin)
    }

    public async send(): Promise<any> {
        console.log('id', ++this.#id)

        const response = await this.pool.request({
            method: 'GET',
            path: this.path,
        })

        return response
    }

    protected createPool(origin: string) {
        const pool = new Pool(origin, {})

        pool.on('connect', () => console.log(`Connected`))
        pool.on('disconnect', (_, __, error) => console.log(`Disconnected`, error))
        pool.on('connectionError', (_, __, error) => console.log(`Connection Error`, error))
        pool.on('drain', () => console.log(`Pool drained`))

        return pool
    }
}

// Test normal requests
const sender = new Sender('https://httpbin.org/get')

console.log('Testing normal requests...')
await Promise.all([sender.send(), sender.send(), sender.send()])
await sender.send()
await sender.send()
await sender.send()

// Test connection error by using invalid URL
console.log('\nTesting connection error...')
const invalidSender = new Sender('https://invalid-domain-that-does-not-exist.com/test')

try {
    await invalidSender.send()
} catch (error) {
    console.log('Expected error caught:', error.message)
}

// Test drain event by creating many concurrent requests
console.log('\nTesting drain event with many concurrent requests...')
const drainSender = new Sender('https://httpbin.org/delay/1')
const requests = Array.from({ length: 20 }, () => drainSender.send().catch((error) => console.log('Request failed:', error.message)))
await Promise.allSettled(requests)

// Monitor pool stats
const statsInterval = setInterval(() => {
    console.log('\nPool stats:', sender['pool'].stats)
    console.log('Invalid sender stats:', invalidSender['pool'].stats)
    console.log('Drain sender stats:', drainSender['pool'].stats)
}, 5000)

// Clean up after 30 seconds
setTimeout(() => {
    clearInterval(statsInterval)
    console.log('\nTest completed')
    process.exit(0)
}, 30_000)
