import { tap } from '@kdt310722/utils/function'
import { RequestLogger } from '../modules/request-logger/request-logger'
import { WorkerManager } from '../modules/workers/manager'
import { registerWorkerModule } from '../modules/workers/module-manager'
import { createChildLogger } from './logger'

registerWorkerModule(RequestLogger)

let _worker: WorkerManager | undefined

export function getWorker() {
    if (!_worker) {
        throw new Error('Worker is not initialized yet')
    }

    return _worker
}

export async function initializeWorker() {
    const logger = createChildLogger('core:worker')
    const timer = tap(logger.createTimer(), () => logger.info('Initializing worker...'))
    const worker = _worker = new WorkerManager()

    worker.on('error', (error) => logger.exit(1, 'error', 'Worker error', error))
    worker.on('exit', (code) => logger.exit(code, 'info', `Worker exited with code ${code}`))
    worker.on('unhandledMessage', (message) => logger.warn('Unhandled message from worker', message))

    await worker.start([import.meta.url]).then(() => {
        logger.stopTimer(timer, 'info', 'Worker started successfully!')
    })
}
