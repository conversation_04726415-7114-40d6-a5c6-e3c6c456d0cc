import type { HttpRequestHandler } from '../modules/jsonrpc-server/types'
import { highlight, LogLevel, message } from '@kdt310722/logger'
import { createSuccessResponseMessage } from '@kdt310722/rpc'
import { stringifyError } from '@kdt310722/utils/error'
import { tap } from '@kdt310722/utils/function'
import { config } from '../config'
import { JsonRpcError, JsonRpcErrorCode } from '../errors/json-rpc-error'
import { JsonRpcServer } from '../modules/jsonrpc-server/server'
import { RequestLogger } from '../modules/request-logger/request-logger'
import { createChildLogger } from './logger'
import { getWorker } from './worker'

let handler_: HttpRequestHandler | undefined
let requestLogger: RequestLogger | undefined

export function setJsonRpcRequestHandler(handler: HttpRequestHandler) {
    handler_ ??= handler
}

export const getRequestLogger = () => requestLogger ??= getWorker().getModule(RequestLogger)

const handler: HttpRequestHandler = async (info) => {
    if (info.message.method === 'ping') {
        return { body: createSuccessResponseMessage(info.message.id, 'pong') }
    }

    if (!handler_) {
        throw new JsonRpcError(JsonRpcErrorCode.METHOD_NOT_FOUND, `Method ${info.message.method} not found`)
    }

    getRequestLogger().add(info)

    return await handler_(info)
}

const logger = createChildLogger('core:server')
const endLogger = logger.child({ name: 'requests:end' })
const server = new JsonRpcServer({ ...config.server, httpHandler: handler }, { maxBodySize: config.server.maxBodySize })

server.on('aborted', (id) => {
    getRequestLogger().abort(id)
    logger.debug(message(() => `JSON-RPC request ${highlight(`#${id}`)} was aborted`))
})

server.on('end', (id, response) => {
    getRequestLogger().end(id, response)
    endLogger.debug(message(() => `JSON-RPC request ${highlight(`#${id}`)} ended`), { response })
})

server.on('error', (id, error) => {
    getRequestLogger().error(id, error)
    logger.log(error.isClientError() ? LogLevel.DEBUG : LogLevel.ERROR, message(() => `JSON-RPC request ${highlight(`#${id}`)} failed with error: ${highlight(stringifyError(error))}`))
})

export async function startServer() {
    const timer = tap(logger.createTimer(), () => logger.info('Starting JSON-RPC server...'))

    await server.start().then(() => {
        logger.stopTimer(timer, 'info', `JSON-RPC server started and listening on: ${highlight(`${config.server.host}:${config.server.port}`)}`)
    })
}
