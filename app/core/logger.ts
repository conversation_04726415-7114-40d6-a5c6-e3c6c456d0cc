import { isMainThread } from 'node:worker_threads'
import { createDefaultLogger } from '@kdt310722/logger'
import { config } from '../config'

export const logger = createDefaultLogger({ ...config.logger, name: `${isMainThread ? 'main' : 'worker'}${config.logger.name?.length ? `:${config.logger.name}` : ''}`, prettier: { showName: config.logger.showName } })

logger.handleRejections()
logger.handleExceptions()

export const createChildLogger = (name: string) => logger.child({ name })
