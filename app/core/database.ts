import { tap } from '@kdt310722/utils/function'
import { pTap } from '@kdt310722/utils/promise'
import { DataSource } from 'typeorm'
import { config } from '../config'
import { DatabaseLogger } from '../utils/database/logger'
import { NamingStrategy } from '../utils/database/naming-strategy'
import { appPath } from '../utils/path'
import { createChildLogger } from './logger'

let _database: DataSource | undefined

export async function initializeDatabase() {
    const logger = createChildLogger('core:database')
    const timer = tap(logger.createTimer(), () => logger.info('Initializing database...'))

    const database = _database = new DataSource({
        ...config.database,
        entities: [appPath('entities', '*.ts'), appPath('modules/*/entities', '*.ts')],
        logger: new DatabaseLogger(logger, config.database.logging),
        namingStrategy: new NamingStrategy(),
    })

    return database.initialize().then(pTap(() => logger.stopTimer(timer, 'info', 'Database initialized!')))
}

export const database = _database!
