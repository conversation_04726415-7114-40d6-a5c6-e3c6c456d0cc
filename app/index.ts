import { tap } from '@kdt310722/utils/function'
import { logger } from './core/logger'
import { startServer } from './core/server'
import { initializeWorker } from './core/worker'
import 'reflect-metadata'

async function main() {
    const timer = tap(logger.createTimer(), () => logger.info('Starting application...'))

    await initializeWorker()
    await startServer()

    logger.stopTimer(timer, 'info', 'Application started!')
}

main().catch((error) => {
    logger.exit(1, 'fatal', 'Failed to start application', error)
})
