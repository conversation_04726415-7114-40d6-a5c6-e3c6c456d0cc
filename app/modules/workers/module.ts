import type { Awaitable } from '@kdt310722/utils/promise'
import type { DataSource } from 'typeorm'
import { isMainThread, type Worker } from 'node:worker_threads'
import { createModuleMessage } from './utils/messages'

export interface WorkerModuleContext {
    database: DataSource
}

export abstract class WorkerModule<T = unknown> {
    protected readonly worker?: Worker
    protected readonly context?: WorkerModuleContext

    public constructor(worker?: Worker, context?: WorkerModuleContext) {
        if (!worker && isMainThread) {
            throw new Error('Worker is required')
        }

        this.worker = worker
        this.context = context
    }

    public get database() {
        return this.context!.database
    }

    public abstract handleMessage(data: T): Awaitable<void>

    protected postMessage(data: T) {
        this.worker?.postMessage(createModuleMessage(this.constructor.name, data))
    }
}
