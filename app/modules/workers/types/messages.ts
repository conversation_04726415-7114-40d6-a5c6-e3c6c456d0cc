import type { WorkerMessageType } from '../constants'

export interface BaseWorkerMessage {
    type: WorkerMessageType
}

export interface WorkerStartedMessageData {
    availableModules: string[]
}

export interface WorkerStartedMessage extends BaseWorkerMessage {
    type: WorkerMessageType.STARTED
    data: WorkerStartedMessageData
}

export interface WorkerModuleMessage<T = unknown> extends BaseWorkerMessage {
    type: WorkerMessageType.MODULE
    module: string
    data: T
}

export type WorkerMessage = WorkerStartedMessage | WorkerModuleMessage
