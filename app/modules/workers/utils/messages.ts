import type { BaseWorkerMessage, WorkerModuleMessage, WorkerStartedMessage } from '../types/messages'
import { isKeyOf, isKeysOf, isObject } from '@kdt310722/utils/object'
import { WorkerMessageType } from '../constants'

export function isWorkerMessage(message: unknown): message is BaseWorkerMessage {
    return isObject(message) && isKeyOf(message, 'type')
}

export function isStartedMessage(message: BaseWorkerMessage): message is WorkerStartedMessage {
    return message.type === WorkerMessageType.STARTED
}

export function isModuleMessage<T = unknown>(message: BaseWorkerMessage): message is WorkerModuleMessage<T> {
    return message.type === WorkerMessageType.MODULE && isKeysOf(message, ['module', 'data'])
}

export function createModuleMessage<T = unknown>(module: string, data: T): WorkerModuleMessage<T> {
    return { type: WorkerMessageType.MODULE, module, data }
}
