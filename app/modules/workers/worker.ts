import type { WorkerModule } from './module'
import { parentPort, workerData } from 'node:worker_threads'
import { initializeDatabase } from '../../core/database'
import { createChildLogger } from '../../core/logger'
import { WorkerMessageType } from './constants'
import { getAvailableWorkerModules } from './module-manager'
import { isModuleMessage, isWorkerMessage } from './utils/messages'

const logger = createChildLogger('worker')
const additionalPaths = workerData.additionalPaths ?? []
const modules: Record<string, WorkerModule> = {}

async function handleModuleMessage(moduleName: string, data: unknown) {
    const workerModule = modules[moduleName]

    if (workerModule) {
        await workerModule.handleMessage(data)
    } else {
        logger.warn(`Received message for unregistered module: ${moduleName}`, data)
    }
}

parentPort?.on('message', (message) => {
    if (isWorkerMessage(message) && isModuleMessage(message)) {
        handleModuleMessage(message.module, message.data)

        return
    }

    logger.warn('Received unhandled message from main thread', message)
})

async function startWorker() {
    for (const path of additionalPaths) {
        await import(path)
    }

    const database = await initializeDatabase()

    for (const [name, module] of Object.entries(getAvailableWorkerModules())) {
        modules[name] = new module(undefined, { database })
    }
}

startWorker().then(() => {
    parentPort?.postMessage({ type: WorkerMessageType.STARTED, data: { availableModules: Object.keys(modules) } })
})
