import type { Constructor } from '@kdt310722/utils/common'
import type { WorkerModule } from './module'
import type { WorkerMessage } from './types/messages'
import { Worker } from 'node:worker_threads'
import { Emitter } from '@kdt310722/utils/event'
import { createDeferred } from '@kdt310722/utils/promise'
import { WORKER_EXEC_ARGV } from './constants'
import { getAvailableWorkerModules } from './module-manager'
import { isStartedMessage, isWorkerMessage } from './utils/messages'

export type WorkerManagerEvents = {
    error: (error: unknown) => void
    exit: (code: number) => void
    message: (message: WorkerMessage) => void
    unhandledMessage: (message: unknown) => void
}

export class WorkerManager extends Emitter<WorkerManagerEvents> {
    public readonly modules: Record<string, WorkerModule> = {}

    protected isStarting = false
    protected isStarted = false

    #worker?: Worker

    public get worker() {
        if (!this.#worker) {
            throw new Error('Worker is not started yet')
        }

        return this.#worker
    }

    public getModule<T>(module: Constructor<T>) {
        if (!this.modules[module.name]) {
            throw new Error(`Module ${module.name} is not registered`)
        }

        return this.modules[module.name] as T
    }

    public async start(additionalPaths: string[] = []) {
        if (this.isStarting || this.isStarted || this.#worker) {
            return
        }

        this.isStarting = true

        const started = createDeferred<void>()
        const worker = this.#worker = new Worker(new URL('worker.ts', import.meta.url), { execArgv: WORKER_EXEC_ARGV, argv: process.argv, workerData: { additionalPaths } })
        const handleError = (error: unknown) => (started.isSettled ? this.handleError(error) : started.reject(error))

        worker.on('error', handleError)
        worker.on('messageerror', handleError)
        worker.on('exit', (code) => (started.isSettled ? this.handleExit(code) : started.reject(new Error(`Worker exited unexpectedly with code ${code}`))))

        worker.on('message', (message) => {
            if (isWorkerMessage(message)) {
                if (isStartedMessage(message)) {
                    this.registerModules(worker, message.data.availableModules)
                    started.resolve()
                } else {
                    this.handleMessage(message as WorkerMessage)
                }
            } else {
                if (started.isSettled) {
                    this.emit('unhandledMessage', message)
                } else {
                    started.reject(Object.assign(new Error('Received unhandled message from worker'), { message }))
                }
            }
        })

        return started.then(() => (this.isStarted = true)).finally(() => (this.isStarting = false))
    }

    protected registerModules(worker: Worker, registeredModulesInWorker: string[]) {
        const mainModules = getAvailableWorkerModules()

        for (const moduleName of registeredModulesInWorker) {
            if (mainModules[moduleName]) {
                this.modules[moduleName] = new mainModules[moduleName](worker)
            }
        }
    }

    protected handleMessage(message: WorkerMessage) {
        this.emit('message', message)
    }

    protected handleError(error: unknown) {
        this.emit('error', error)
    }

    protected handleExit(code: number) {
        this.reset()
        this.emit('exit', code)
    }

    protected reset() {
        this.isStarting = false
        this.isStarted = false
        this.#worker = undefined
    }
}
