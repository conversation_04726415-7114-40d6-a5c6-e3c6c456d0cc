import type { JsonRpcError } from '../../errors/json-rpc-error'
import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../jsonrpc-server/types'
import type { RequestProcessor } from './request-processor'
import { omit } from '@kdt310722/utils/object'
import { serializeError } from 'serialize-error'
import { WorkerModule } from '../workers/module'

export interface RequestLoggerAddMessage {
    action: 'add'
    data: Omit<JsonRpcHttpRequestInfo, 'signal'>
}

export interface RequestLoggerAbortMessage {
    action: 'abort'
    data: string
}

export interface RequestLoggerEndMessageData {
    id: string
    response?: JsonRpcHttpResponse
}

export interface RequestLoggerEndMessage {
    action: 'end'
    data: RequestLoggerEndMessageData
}

export interface RequestLoggerErrorMessageData {
    id: string
    error: Record<string, unknown>
}

export interface RequestLoggerErrorMessage {
    action: 'error'
    data: RequestLoggerErrorMessageData
}

export type RequestLoggerMessage = RequestLoggerAddMessage | RequestLoggerAbortMessage | RequestLoggerEndMessage | RequestLoggerErrorMessage

export class RequestLogger extends WorkerModule<RequestLoggerMessage> {
    protected processor?: Promise<RequestProcessor>

    public add(request: JsonRpcHttpRequestInfo) {
        this.postMessage({ action: 'add', data: omit(request, 'signal') })
    }

    public abort(id: string) {
        this.postMessage({ action: 'abort', data: id })
    }

    public end(id: string, response?: JsonRpcHttpResponse) {
        this.postMessage({ action: 'end', data: { id, response } })
    }

    public error(id: string, error: JsonRpcError) {
        this.postMessage({ action: 'error', data: { id, error: serializeError(error) } })
    }

    public async handleMessage(message: RequestLoggerMessage) {
        const processor = await (this.processor ??= import('./request-processor').then((m) => new m.RequestProcessor(this.database)))

        switch (message.action) {
            case 'add':
                processor.add(message.data)
                break
            case 'abort':
                await processor.abort(message.data)
                break
            case 'end':
                await processor.end(message.data.id, message.data.response)
                break
            case 'error':
                processor.error(message.data.id, message.data.error)
                break
            default:
                throw new Error(`Unknown action: ${message['action']}`)
        }
    }
}
