import type { JsonRpcHttpRequestInfo } from '../../jsonrpc-server/types'
import { Request } from '../../../entities/request'

export const toRequestEntity = (request: Omit<JsonRpcHttpRequestInfo, 'signal'>) => {
    const entity = new Request()

    entity.body = request.message
    entity.headers = request.headers
    entity.isAborted = false
    entity.error = null
    entity.processTime = request.processTime
    entity.executionTime = null
    entity.response = null
    entity.clientIp = request.clientIp

    return entity
}
