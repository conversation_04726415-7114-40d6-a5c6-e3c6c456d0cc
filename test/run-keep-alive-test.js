#!/usr/bin/env node

/**
 * Test runner for Undici Keep-Alive performance test
 *
 * Usage:
 *   node test/run-keep-alive-test.js
 *   node test/run-keep-alive-test.js --requests=20 --delay=1000
 *   node test/run-keep-alive-test.js --endpoint=https://api.example.com
 */

// Register SWC for TypeScript compilation using --import flag
// This is handled by the Node.js --import flag instead of programmatic registration

// Parse command line arguments
const args = process.argv.slice(2)
const options = {}

args.forEach((arg) => {
    if (arg.startsWith('--')) {
        const [key, value] = arg.slice(2).split('=')
        options[key] = value
    }
})

// Default configuration
const config = {
    requests: Number.parseInt(options.requests) || 10,
    delay: Number.parseInt(options.delay) || 500,
    endpoint: options.endpoint || 'https://httpbin.org',
    help: options.help || options.h,
}

if (config.help) {
    console.log(`
🧪 Undici Keep-Alive Performance Test Runner

Usage:
  node test/run-keep-alive-test.js [options]

Options:
  --requests=N     Number of requests to make (default: 10)
  --delay=N        Delay between requests in ms (default: 500)
  --endpoint=URL   Test endpoint URL (default: https://httpbin.org)
  --help, -h       Show this help message

Examples:
  node test/run-keep-alive-test.js
  node test/run-keep-alive-test.js --requests=20 --delay=1000
  node test/run-keep-alive-test.js --endpoint=https://api.github.com

Note: Make sure the endpoint supports CORS and GET requests to /get or similar.
`)

    process.exit(0)
}

console.log('🔧 Loading test module...')

// Dynamic import the test module
const { UndiciKeepAliveTest } = await import('./test-undici-keep-alive.ts')

console.log(`🚀 Starting test with configuration:`)
console.log(`   Requests: ${config.requests}`)
console.log(`   Delay: ${config.delay}ms`)
console.log(`   Endpoint: ${config.endpoint}`)
console.log('')

const test = new UndiciKeepAliveTest(config.endpoint)

try {
    await test.runTest(config.requests, config.delay)
    console.log('\n🎉 Test completed successfully!')
} catch (error) {
    console.error('\n❌ Test failed:', error.message)

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        console.error('💡 Tip: Check if the endpoint is accessible and supports the required HTTP methods')
    }

    process.exit(1)
} finally {
    await test.cleanup()
}
