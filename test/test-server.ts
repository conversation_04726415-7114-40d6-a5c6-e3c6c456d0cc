import { createServer, type IncomingMessage, type Server, type ServerResponse } from 'node:http'

interface TestServerOptions {
    port?: number
    keepAliveTimeout?: number
    headersTimeout?: number
}

export class TestServer {
    protected readonly server: Server
    protected readonly port: number
    protected readonly keepAliveTimeout: number

    public constructor(options: TestServerOptions = {}) {
        this.port = options.port || 0
        this.keepAliveTimeout = options.keepAliveTimeout || 5000

        this.server = createServer(this.handleRequest.bind(this))
        this.server.keepAliveTimeout = this.keepAliveTimeout
        this.server.headersTimeout = options.headersTimeout || 60_000
        this.server.requestTimeout = 0
        this.server.timeout = 0

        console.log(`🔧 Server configured with keepAliveTimeout: ${this.keepAliveTimeout}ms`)

        this.server.on('connection', (socket) => {
            console.log(`🔗 Server: New socket connection established`)

            socket.on('close', () => {
                console.log(`❌ Server: Socket connection closed`)
            })
        })
    }

    public async start() {
        return new Promise<void>((resolve, reject) => {
            this.server.listen(this.port, (error?: Error) => {
                if (error) {
                    reject(error)

                    return
                }

                resolve()
            })
        })
    }

    public async stop() {
        return new Promise<void>((resolve) => {
            this.server.close(() => resolve())
        })
    }

    public getPort() {
        const address = this.server.address()

        if (typeof address === 'object' && address !== null) {
            return address.port
        }

        return this.port
    }

    public getUrl() {
        return `http://localhost:${this.getPort()}`
    }

    protected handleRequest(req: IncomingMessage, res: ServerResponse) {
        const method = req.method?.toUpperCase()
        const url = req.url || '/'
        const timestamp = Date.now()

        console.log(`📥 Server: ${method} ${url} - Connection: ${req.headers.connection}`)

        res.setHeader('Content-Type', 'application/json')
        res.setHeader('Connection', 'keep-alive')
        res.setHeader('Keep-Alive', `timeout=${Math.floor(this.keepAliveTimeout / 1000)}`)

        console.log(`📤 Server: Responding with Keep-Alive timeout=${Math.floor(this.keepAliveTimeout / 1000)}s`)

        if (method === 'GET' && url === '/get') {
            const responseData = {
                method: 'GET',
                url: '/get',
                timestamp,
                headers: req.headers,
                keepAliveTimeout: this.keepAliveTimeout,
                serverTime: new Date().toISOString(),
            }

            res.writeHead(200)
            res.end(JSON.stringify(responseData))

            return
        }

        if (method === 'POST' && url === '/post') {
            let body = ''

            req.on('data', (chunk) => {
                body += chunk.toString()
            })

            req.on('end', () => {
                const responseData = {
                    method: 'POST',
                    url: '/post',
                    timestamp,
                    headers: req.headers,
                    body: body ? JSON.parse(body) : null,
                    keepAliveTimeout: this.keepAliveTimeout,
                    serverTime: new Date().toISOString(),
                }

                res.writeHead(200)
                res.end(JSON.stringify(responseData))
            })

            return
        }

        if (method === 'POST' && url === '/post-404') {
            res.writeHead(404)

            res.end(JSON.stringify({
                error: 'Not Found',
                message: 'POST endpoint not found',
                timestamp,
            }))

            return
        }

        if (method === 'GET' && url === '/error-500') {
            res.writeHead(500)

            res.end(JSON.stringify({
                error: 'Internal Server Error',
                message: 'Simulated server error',
                timestamp,
            }))

            return
        }

        if (method === 'HEAD' && url === '/head') {
            res.setHeader('X-Timestamp', timestamp.toString())
            res.setHeader('X-Keep-Alive-Timeout', this.keepAliveTimeout.toString())
            res.writeHead(200)
            res.end()

            return
        }

        res.writeHead(404)
        res.end(JSON.stringify({ error: 'Not Found', timestamp }))
    }
}
